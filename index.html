<!DOCTYPE html>
<html lang="ml">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>കുമരനെല്ലൂർ വായിക്കപ്പെടുന്നു</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Google Fonts: Manjari for Malayalam & Inter for English -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Manjari:wght@400;700&family=Inter:wght@400;700&display=swap"
      rel="stylesheet"
    />

    <!-- Custom Styles and Animations -->
    <style>
      /* Apply custom fonts */
      body {
        font-family: "Manjari", sans-serif;
        background-color: #fffcf5; /* A warm off-white, like old paper */
        color: #4c3d3d; /* A deep, earthy brown for text */
      }

      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        font-family: "Manjari", sans-serif;
        font-weight: 700;
      }

      /* Traditional Kerala Mural Art inspired colors */
      :root {
        --primary-color: #c06b3e; /* A terracotta/saffron shade */
        --secondary-color: #4a5d23; /* A deep olive green */
        --accent-color: #b48811; /* A dull gold */
        --bg-color: #fffcf5;
        --text-color: #4c3d3d;
      }

      /* Hero section styling */
      .hero-section {
        background-image: linear-gradient(
            rgba(0, 0, 0, 0.4),
            rgba(0, 0, 0, 0.4)
          ),
          url("images/hero-background.jpg");
        background-size: cover;
        background-position: center;
        background-attachment: fixed; /* Parallax effect */
      }

      .hero-title {
        text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.7);
      }

      /* Scroll-reveal animations */
      .reveal {
        opacity: 0;
        transform: translateY(50px);
        transition: opacity 0.8s ease-out, transform 0.8s ease-out;
      }

      .reveal.visible {
        opacity: 1;
        transform: translateY(0);
      }

      /* Card styles for writers */
      .writer-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      .writer-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
          0 10px 10px -5px rgba(0, 0, 0, 0.04);
      }

      /* Book cover styles */
      .book-cover {
        transition: transform 0.3s ease;
      }
      .book-cover:hover {
        transform: scale(1.05);
      }

      /* Modal styling */
      .modal {
        transition: opacity 0.3s ease-in-out;
      }
      .modal-content {
        transition: transform 0.3s ease-in-out;
        transform: translateY(50px);
      }
      .modal.open .modal-content {
        transform: translateY(0);
      }

      /* Custom border with traditional pattern feel */
      .kerala-border {
        border: 2px solid var(--primary-color);
        border-image: linear-gradient(
            to right,
            var(--primary-color),
            var(--accent-color)
          )
          1;
      }

      /* Accordion for author works */
      .accordion-content {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.5s ease-in-out;
      }
    </style>
  </head>
  <body class="bg-[var(--bg-color)] text-[var(--text-color)]">
    <!-- Hero Section -->
    <section
      class="hero-section h-screen w-full flex flex-col items-center justify-center text-white p-4"
    >
      <div class="text-center">
        <h1
          class="hero-title text-5xl md:text-8xl lg:text-9xl font-bold leading-tight"
          style="line-height: 1.2"
        >
          കുമരനെല്ലൂർ<br />വായിക്കപ്പെടുന്നു
        </h1>
        <p class="mt-4 text-xl md:text-2xl font-light">
          അക്ഷരങ്ങളുടെ ലോകത്തേക്കൊരു യാത്ര
        </p>
      </div>
      <div class="absolute bottom-10 animate-bounce">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-8 w-8 text-white"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </div>
    </section>

    <main class="container mx-auto px-6 py-16 md:py-24">
      <!-- Section 01: Introduction -->
      <section class="reveal text-center max-w-4xl mx-auto mb-20">
        <h2 class="text-3xl md:text-4xl text-primary-color mb-6">
          മലയാളത്തിൻ്റെ ഇതിഹാസ ഭൂമി
        </h2>
        <p class="text-lg md:text-xl leading-relaxed">
          കുമരനെല്ലൂർ നിരവധി പ്രതിഭകളെ ഗർഭം ചുമന്ന ദേശം. അക്ഷരങ്ങൾക്ക് നിറം
          പകർന്നു നൽകി മലയാളത്തിന്റെ ഇതിഹാസമായി മാറിയ എം.ടി യും അക്കിത്തവും, പി
          സുരേന്ദ്രനും നടന്നു പോയ വഴികൾ. ലോകവായനയിൽ ഇടം പിടിച്ച നിരവധി
          അവാർഡുകൾക്ക് ജന്മം നൽകിയ മണ്ണ്.
        </p>
      </section>

      <!-- Section 02: Book Covers Gallery -->
      <section class="reveal mb-20">
        <h2 class="text-3xl md:text-4xl text-center text-primary-color mb-10">
          തിരഞ്ഞെടുത്ത പുസ്തകങ്ങൾ
        </h2>
        <div
          class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-6"
        >
          <!-- Placeholder book covers -->
          <img
            src="images/book-nalukettu.jpg"
            alt="Book Cover for Nalukettu"
            class="book-cover rounded-lg shadow-lg w-full"
          />
          <img
            src="images/book-randamoozham.jpg"
            alt="Book Cover for Randamoozham"
            class="book-cover rounded-lg shadow-lg w-full"
          />
          <img
            src="images/book-irupatham.jpg"
            alt="Book Cover for Irupatham Noottandinte Ithihasam"
            class="book-cover rounded-lg shadow-lg w-full"
          />
          <img
            src="images/book-asuravithu.jpg"
            alt="Book Cover for Asuravithu"
            class="book-cover rounded-lg shadow-lg w-full"
          />
          <img
            src="images/book-manju.jpg"
            alt="Book Cover for Manju"
            class="book-cover rounded-lg shadow-lg w-full"
          />
          <img
            src="images/book-balidarsanam.jpg"
            alt="Book Cover"
            class="book-cover rounded-lg shadow-lg w-full"
          />
        </div>
      </section>

      <!-- Section 03 & 04: Writers -->
      <section class="reveal mb-20">
        <h2 class="text-3xl md:text-4xl text-center text-primary-color mb-10">
          ഇതിഹാസങ്ങളുടെ ശില്പികൾ
        </h2>
        <div class="grid md:grid-cols-2 gap-10 max-w-5xl mx-auto">
          <!-- Writer 1: MT -->
          <div
            id="writer-mt"
            class="writer-card cursor-pointer bg-white p-6 rounded-xl shadow-lg border border-gray-200 text-center"
          >
            <img
              src="images/writer-mt.jpg"
              alt="എം.ടി വാസുദേവൻ നായർ"
              class="w-32 h-32 rounded-full mx-auto mb-4 border-4 border-accent-color"
            />
            <h3 class="text-2xl font-bold text-secondary-color mb-2">
              എം.ടി വാസുദേവൻ നായർ
            </h3>
            <p class="mb-4">
              മലയാള സാഹിത്യത്തിലെ ഇതിഹാസകാരൻ. നോവലിസ്റ്റ്, തിരക്കഥാകൃത്ത്,
              സംവിധായകൻ എന്നീ നിലകളില്‍ പ്രശസ്തന്‍.
            </p>
            <span class="text-accent-color font-bold"
              >കൂടുതലറിയാൻ ക്ലിക്ക് ചെയ്യുക</span
            >
          </div>

          <!-- Writer 2: Akkitham -->
          <div
            id="writer-akkitham"
            class="writer-card cursor-pointer bg-white p-6 rounded-xl shadow-lg border border-gray-200 text-center"
          >
            <img
              src="images/writer-akkitham.jpg"
              alt="അക്കിത്തം അച്യുതൻ നമ്പൂതിരി"
              class="w-32 h-32 rounded-full mx-auto mb-4 border-4 border-accent-color"
            />
            <h3 class="text-2xl font-bold text-secondary-color mb-2">
              അക്കിത്തം അച്യുതൻ നമ്പൂതിരി
            </h3>
            <p class="mb-4">
              ജ്ഞാനപീഠം പുരസ്കാര ജേതാവായ മഹാനായ കവി. മാനവികതയുടെയും
              സ്നേഹത്തിൻ്റെയും സന്ദേശങ്ങൾ പകർന്ന കവിതകൾ.
            </p>
            <span class="text-accent-color font-bold"
              >കൂടുതലറിയാൻ ക്ലിക്ക് ചെയ്യുക</span
            >
          </div>
        </div>
      </section>

      <!-- Section 05: Summary -->
      <section class="reveal text-center max-w-4xl mx-auto mb-20">
        <h2 class="text-3xl md:text-4xl text-primary-color mb-6">
          വായനയുടെ ലോകം
        </h2>
        <p class="text-lg md:text-xl leading-relaxed">
          തിരഞ്ഞെടുക്കുന്ന പുസ്തകത്തെ കുറിച്ചുള്ള സംക്ഷിപ്തം. കഥയുടെ, കവിതയുടെ,
          നോവലിൻ്റെ ഉൾക്കാമ്പ് തൊട്ടറിഞ്ഞ്, ഓരോ വരിയിലും ഒളിപ്പിച്ചുവെച്ച
          ജീവിതഗന്ധിയായ അനുഭവങ്ങളെ ഞങ്ങൾ നിങ്ങൾക്കായി പങ്കുവെക്കുന്നു.
        </p>
      </section>
    </main>

    <!-- Section 06 & 07: Footer -->
    <footer class="bg-secondary-color text-white py-12">
      <div class="container mx-auto text-center px-6">
        <h3 class="text-2xl text-accent-color mb-4">നന്ദി</h3>
        <p class="mb-6 max-w-2xl mx-auto">
          ജൂൺ 19 വായനാ ദിനത്തിൽ അയ്യൂബി ഗേൾസ് വില്ലേജിനൊപ്പം ഈ രചനയുടെ ലോകത്ത്
          ഒന്നിച്ചു നടന്നതിന്, നിങ്ങളുടെ വായനക്ക് കടപ്പാടുകൾ.
        </p>
        <div class="border-t border-accent-color w-1/4 mx-auto my-4"></div>
        <p class="text-lg font-bold">Ayyoobi Girls Village</p>
      </div>
    </footer>

    <!-- Modal for Writer Details -->
    <div
      id="writer-modal"
      class="modal fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center p-4 z-50 opacity-0 pointer-events-none"
    >
      <div
        class="modal-content bg-[var(--bg-color)] text-[var(--text-color)] rounded-lg shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto kerala-border"
      >
        <div class="p-6 md:p-8">
          <!-- Modal Header -->
          <div class="flex justify-between items-start mb-6">
            <div class="flex items-center">
              <img
                id="modal-img"
                src=""
                alt="Writer"
                class="w-24 h-24 md:w-32 md:h-32 rounded-full border-4 border-accent-color mr-6"
              />
              <div>
                <h2
                  id="modal-name"
                  class="text-3xl md:text-4xl font-bold text-primary-color"
                ></h2>
                <p id="modal-bio" class="text-md text-secondary-color"></p>
              </div>
            </div>
            <button
              id="modal-close"
              class="text-3xl text-text-color hover:text-primary-color"
            >
              &times;
            </button>
          </div>

          <!-- Modal Body: Works List -->
          <div>
            <h3 class="text-2xl text-secondary-color font-bold mb-4">
              പ്രധാന കൃതികളും നിരൂപണങ്ങളും
            </h3>
            <div id="modal-works" class="space-y-3">
              <!-- Works will be injected here by JS -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      // --- Data for Writers ---
      const writersData = {
        mt: {
          name: "എം.ടി വാസുദേവൻ നായർ",
          image: "images/writer-mt.jpg",
          bio: "മലയാള സാഹിത്യത്തിലെ ഇതിഹാസകാരൻ.",
          works: [
            {
              title: "നാലുകെട്ട്",
              review:
                "സാമൂഹിക വ്യവസ്ഥിതിയുടെ നേർക്കാഴ്ചയും വ്യക്തിബന്ധങ്ങളുടെ തീവ്രതയും വരച്ചുകാട്ടുന്ന, മലയാളത്തിലെ എക്കാലത്തെയും മികച്ച ക്ലാസിക് നോവലുകളിലൊന്ന്.",
            },
            {
              title: "അസുരവിത്ത്",
              review:
                "അധികാരത്തിനും വിശ്വാസത്തിനും ഇടയിൽ ഞെരിഞ്ഞമരുന്ന ഒരു മനുഷ്യൻ്റെ ആത്മസംഘർഷങ്ങളുടെ കഥ പറയുന്ന ശക്തമായ രചന.",
            },
            {
              title: "മഞ്ഞ്",
              review:
                "കാത്തിരിപ്പിൻ്റെയും പ്രതീക്ഷയുടെയും വിഷാദഭാവങ്ങൾ മനോഹരമായി ആവിഷ്കരിച്ച, മലയാളികളെ ഏറെ സ്വാധീനിച്ച നോവൽ.",
            },
            {
              title: "കാലം",
              review:
                "സേതു എന്ന യുവാവിൻ്റെ ജീവിതത്തിലൂടെ കാലത്തിൻ്റെ മാറ്റങ്ങളെയും വ്യക്തിപരമായ നഷ്ടങ്ങളെയും അതിമനോഹരമായി അവതരിപ്പിക്കുന്നു.",
            },
            {
              title: "രണ്ടാമൂഴം",
              review:
                "മഹാഭാരതത്തെ ഭീമൻ്റെ കാഴ്ചപ്പാടിലൂടെ പുനരാവിഷ്കരിക്കുന്ന, ഇന്ത്യൻ സാഹിത്യത്തിലെ തന്നെ ഒരു നാഴികക്കല്ലായ നോവൽ.",
            },
            {
              title: "വിലാപയാത്ര",
              review:
                "മരണവും ജീവിതവും തമ്മിലുള്ള ബന്ധത്തെക്കുറിച്ചുള്ള ആഴത്തിലുള്ള ചിന്തകൾക്ക് വഴിവെക്കുന്ന ഹൃദയസ്പർശിയായ രചന.",
            },
            {
              title: "അറബിപ്പൊന്ന്",
              review:
                "എൻ.പി. മുഹമ്മദുമായി ചേർന്നെഴുതിയ ഈ നോവൽ പ്രവാസ ജീവിതത്തിൻ്റെയും സൗഹൃദത്തിൻ്റെയും കഥ പറയുന്നു.",
            },
          ],
        },
        akkitham: {
          name: "അക്കിത്തം അച്യുതൻ നമ്പൂതിരി",
          image: "images/writer-akkitham.jpg",
          bio: "ജ്ഞാനപീഠം നേടിയ കവി.",
          works: [
            {
              title: "ഇരുപതാം നൂറ്റാണ്ടിന്റെ ഇതിഹാസം",
              review:
                "സ്നേഹമാണ് ലോകത്തിൻ്റെ അടിസ്ഥാനമെന്നും മനുഷ്യൻ്റെ കണ്ണീരിനാണ് ഏറ്റവും വിലയെന്നും ഉദ്ഘോഷിച്ച, മലയാള കവിതയുടെ ഗതി മാറ്റിയ കൃതി.",
            },
            {
              title: "ബലിദർശനം",
              review:
                "ചരിത്രത്തെയും മിത്തുകളെയും സമന്വയിപ്പിച്ച് മനുഷ്യൻ്റെ നിസ്സഹായതയും വേദനയും ആവിഷ്കരിക്കുന്ന, കേന്ദ്ര സാഹിത്യ അക്കാദമി അവാർഡ് നേടിയ കാവ്യം.",
            },
            {
              title: "വെണ്ണക്കല്ലിന്റെ കഥ",
              review:
                "ലളിതമായ ഭാഷയിൽ ഗഹനമായ തത്വചിന്തകൾ അവതരിപ്പിക്കുന്ന, കുട്ടികൾക്കും മുതിർന്നവർക്കും ഒരുപോലെ ആസ്വദിക്കാൻ കഴിയുന്ന കവിതകൾ.",
            },
            {
              title: "നിമിഷ ക്ഷേത്രം",
              review:
                "ജീവിതത്തിലെ ഓരോ നിമിഷത്തിൻ്റെയും പ്രാധാന്യത്തെ ഓർമ്മിപ്പിക്കുന്ന, ആഴത്തിലുള്ള ദാർശനിക ചിന്തകൾ നിറഞ്ഞ കവിതാ സമാഹാരം.",
            },
            {
              title: "മനസാക്ഷിയുടെ പൂക്കൾ",
              review:
                "വ്യക്തിപരമായ അനുഭവങ്ങളെ സാമൂഹികമായ കാഴ്ചപ്പാടുകളുമായി ബന്ധിപ്പിക്കുന്ന, ചിന്തോദ്ദീപകമായ കവിതകൾ.",
            },
            {
              title: "ഇടിഞ്ഞു പൊളിഞ്ഞ ലോകം",
              review:
                "കാലത്തിൻ്റെ മാറ്റത്തിൽ നഷ്ടപ്പെട്ടുപോകുന്ന മൂല്യങ്ങളെക്കുറിച്ചുള്ള വേദനയും ആശങ്കയും പങ്കുവെക്കുന്ന ശക്തമായ കവിതകൾ.",
            },
            {
              title: "അമൃതഗാഥിക",
              review:
                "സ്നേഹത്തിൻ്റെയും കരുണയുടെയും അമൃതം പകരുന്ന, ആത്മീയതയുടെ തലങ്ങളിലേക്ക് വായനക്കാരെ കൊണ്ടുപോകുന്ന കാവ്യം.",
            },
          ],
        },
      };

      // --- Scroll Reveal Animation ---
      const revealElements = document.querySelectorAll(".reveal");
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              entry.target.classList.add("visible");
            }
          });
        },
        {
          threshold: 0.1,
        }
      );
      revealElements.forEach((el) => observer.observe(el));

      // --- Modal Logic ---
      const modal = document.getElementById("writer-modal");
      const closeModalBtn = document.getElementById("modal-close");
      const mtCard = document.getElementById("writer-mt");
      const akkithamCard = document.getElementById("writer-akkitham");

      const modalName = document.getElementById("modal-name");
      const modalImg = document.getElementById("modal-img");
      const modalBio = document.getElementById("modal-bio");
      const modalWorks = document.getElementById("modal-works");

      const openModal = (writerKey) => {
        const data = writersData[writerKey];
        if (!data) return;

        modalName.textContent = data.name;
        modalImg.src = data.image;
        modalBio.textContent = data.bio;

        modalWorks.innerHTML = ""; // Clear previous works
        data.works.forEach((work) => {
          const workElement = document.createElement("div");
          workElement.className =
            "border border-gray-200 rounded-lg overflow-hidden";
          workElement.innerHTML = `
                    <div class="accordion-header bg-white/60 p-4 cursor-pointer flex justify-between items-center">
                        <h4 class="font-bold text-lg text-secondary-color">${work.title}</h4>
                        <span class="text-accent-color transform transition-transform duration-300">&#9662;</span>
                    </div>
                    <div class="accordion-content bg-white/30">
                        <p class="p-4 text-md leading-relaxed">${work.review}</p>
                    </div>
                `;
          modalWorks.appendChild(workElement);
        });

        modal.classList.remove("opacity-0", "pointer-events-none");
        modal.classList.add("open");
        document.body.style.overflow = "hidden"; // Prevent background scroll
      };

      const closeModal = () => {
        modal.classList.add("opacity-0", "pointer-events-none");
        modal.classList.remove("open");
        document.body.style.overflow = "auto";
      };

      mtCard.addEventListener("click", () => openModal("mt"));
      akkithamCard.addEventListener("click", () => openModal("akkitham"));
      closeModalBtn.addEventListener("click", closeModal);

      // Close modal on outside click
      modal.addEventListener("click", (e) => {
        if (e.target === modal) {
          closeModal();
        }
      });

      // Close modal with Escape key
      document.addEventListener("keydown", (e) => {
        if (e.key === "Escape" && modal.classList.contains("open")) {
          closeModal();
        }
      });

      // --- Accordion Logic for Works ---
      modalWorks.addEventListener("click", function (e) {
        const header = e.target.closest(".accordion-header");
        if (!header) return;

        const content = header.nextElementSibling;
        const arrow = header.querySelector("span");

        // Close all other open accordions
        this.querySelectorAll(".accordion-content").forEach((item) => {
          if (item !== content) {
            item.style.maxHeight = null;
            const otherArrow =
              item.previousElementSibling.querySelector("span");
            if (otherArrow) otherArrow.style.transform = "rotate(0deg)";
          }
        });

        // Toggle current accordion
        if (content.style.maxHeight) {
          content.style.maxHeight = null;
          arrow.style.transform = "rotate(0deg)";
        } else {
          content.style.maxHeight = content.scrollHeight + "px";
          arrow.style.transform = "rotate(180deg)";
        }
      });
    </script>
  </body>
</html>