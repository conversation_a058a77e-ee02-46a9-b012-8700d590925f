# കുമരനെല്ലൂർ വായിക്കപ്പെടുന്നു (Kumaranellur Vayikkapedunnu)

This is a simple, single-page website dedicated to the literary legends of Kumaranellur, created for "വായനാ ദിനം" (Reading Day).

## About the Project

The website showcases prominent writers from Kumaranellur, like <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, along with some of their famous works. It's built with HTML, Tailwind CSS, and a bit of vanilla JavaScript.

## Contributing

If you wish to contribute to this project, please keep the following in mind:

### Image Usage

**Important:** All images in this project are hosted externally on a GitHub Pages site. When adding or changing images, you **must** use the full URL path, not a relative path.

The base URL for the images is: `https://muhammad-shameel-ks.github.io/kumaranallur-vaayikkapedunnu/`

For example, to add a new image named `new-book.jpg` from the `images` folder, the correct path would be:
`https://muhammad-shameel-ks.github.io/kumaranallur-vaayikkapedunnu/images/new-book.jpg`

## Automatic Deployment

This project uses GitHub Actions to automatically deploy the website to GitHub Pages. Any changes pushed to the `main` branch will trigger the deployment workflow defined in `.github/workflows/deploy.yml`.

The site will be published to the `gh-pages` branch automatically.